import { <PERSON>, But<PERSON>, Badge, Typography } from 'antd';
import { Ruler, Package } from 'lucide-react';

const { Title, Text } = Typography;

const SkipCard = ({ skip, onSelect, isSelected = false }) => {
  const handleSelect = () => {
    onSelect(skip);
  };

  return (
    <Card
      className={`
        relative overflow-hidden transition-all duration-300 hover:shadow-xl h-full flex flex-col
        ${isSelected
          ? 'ring-2 ring-blue-500 shadow-lg shadow-blue-500/20'
          : 'hover:shadow-lg'
        }
        bg-slate-800/50 backdrop-blur-sm border-slate-700/50
      `}

      cover={
        <div className="relative h-40 bg-gradient-to-br from-slate-800 to-slate-900 flex items-center justify-center border-b border-slate-700/50">
          {/* Skip Image - Simulated browser window */}
          <div className="w-full h-full bg-slate-900 rounded-t-lg overflow-hidden">
            {/* Browser header */}
            <div className="h-6 bg-slate-800 flex items-center px-2 gap-1">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            </div>

            {/* Skip images grid */}
            <div className="p-2 grid grid-cols-3 gap-1 h-full">
              {[1, 2, 3, 4, 5, 6].map((item) => (
                <div key={item} className="bg-yellow-500 rounded flex items-center justify-center relative">
                  <Package className="w-4 h-4 text-yellow-900" />
                  {item <= 3 && (
                    <div className="absolute top-0 right-0 w-3 h-3 bg-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Yards Badge */}
          <div className="absolute top-2 right-2">
            <Badge
              count={`${skip.yards} Yards`}
              style={{ backgroundColor: '#3b82f6', fontSize: '10px' }}
            />
          </div>
        </div>
      }
    >
      {/* Card Content - Flex Container */}
      <div className="p-4 flex flex-col h-full">
        {/* Skip Title */}
        <div className="mb-4">
          <Title level={4} className="!text-white !mb-2">
            {skip.sizeLabel}
          </Title>
          <Text className="text-slate-300 text-sm">
            {skip.description}
          </Text>
        </div>

      {/* Skip Details - Compact */}
      <div className="space-y-2 mb-4">
        {/* Dimensions & Capacity */}
        <div className="flex items-center gap-2">
          <Ruler className="w-4 h-4 text-blue-400" />
          <Text className="text-slate-300 text-sm">
            {skip.dimensions}
          </Text>
        </div>

        <div className="flex items-center gap-2">
          <Package className="w-4 h-4 text-green-400" />
          <Text className="text-slate-300 text-sm">
            {skip.capacity}
          </Text>
        </div>
      </div>

      {/* Suitable For Tags */}
      <div className="mb-4">
        <Text className="text-slate-400 text-xs mb-2 block">Suitable for:</Text>
        <div className="flex flex-wrap gap-1">
          {skip.suitableFor.slice(0, 2).map((use, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-slate-700/50 text-slate-300 text-xs rounded-full"
            >
              {use}
            </span>
          ))}
          {skip.suitableFor.length > 2 && (
            <span className="px-2 py-1 bg-slate-700/50 text-slate-400 text-xs rounded-full">
              +{skip.suitableFor.length - 2} more
            </span>
          )}
        </div>
      </div>

      {/* Price Section */}
      <div className="mb-4">
        <div className="flex items-baseline gap-2">
          <Title level={2} className="!text-blue-400 !mb-0">
            £{skip.price}
          </Title>
          <div className="text-right">
            <Text className="text-slate-400 text-xs block">Max weight</Text>
            <Text className="text-white text-sm font-semibold">
              {skip.maxWeight}
            </Text>
          </div>
        </div>
        <Text className="text-slate-400 text-sm">
          {skip.hirePeriod}
        </Text>
      </div>

        {/* Action Button - Pushed to bottom */}
        <div className="mt-auto">
          <Button
            type={isSelected ? "primary" : "default"}
            size="large"
            onClick={handleSelect}
            block
            className={`
              ${isSelected
                ? 'bg-blue-600 border-blue-600 text-white'
                : 'bg-white border-white text-slate-900 hover:bg-slate-100'
              }
            `}
          >
            {isSelected ? 'Selected' : 'Select This Skip'}
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default SkipCard;
