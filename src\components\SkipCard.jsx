import { <PERSON>, Button, Badge, Typography } from 'antd';
import { Ruler, Package } from 'lucide-react';

// Import skip images
import skip4Yard from '../assets/images/4yard.jpg';
import skip6Yard from '../assets/images/6yard.jpg';
import skip8Yard from '../assets/images/8yard.jpg';
import skip10Yard from '../assets/images/10yard.jpg';
import skip12Yard from '../assets/images/12yard.jpg';
import skip14Yard from '../assets/images/14yard.jpg';

const { Title, Text } = Typography;

// Image mapping function
const getSkipImage = (yards) => {
  const imageMap = {
    4: skip4Yard,
    6: skip6Yard,
    8: skip8Yard,
    10: skip10Yard,
    12: skip12Yard,
    14: skip14Yard,
  };
  return imageMap[yards] || skip4Yard; // fallback to 4yard image
};

const SkipCard = ({ skip, onSelect, isSelected = false }) => {
  const handleSelect = () => {
    onSelect(skip);
  };

  return (
    <Card
      className={`
        relative overflow-hidden transition-all duration-300 hover:shadow-xl flex flex-col
        ${isSelected
          ? 'ring-2 ring-blue-500 shadow-lg shadow-blue-500/20'
          : 'hover:shadow-lg'
        }
        bg-slate-800/50 backdrop-blur-sm border-slate-700/50
      `}
      style={{ height: '480px' }} // Sabit yükseklik

      cover={
        <div className="relative h-40 overflow-hidden">
          {/* Skip Image */}
          <img
            src={getSkipImage(skip.yards)}
            alt={skip.sizeLabel}
            className="w-full h-full object-cover"
          />

          {/* Size Label - Top Left */}
          <div className="absolute top-2 left-2">
            <div className="bg-black/70 backdrop-blur-sm px-3 py-1 rounded">
              <Title level={5} className="!text-white !mb-0 text-sm">
                {skip.sizeLabel}
              </Title>
            </div>
          </div>

          {/* Popular Badge - Top Right */}
          {skip.popular && (
            <div className="absolute top-2 right-2">
              <Badge
                count="Popular"
                style={{ backgroundColor: '#f97316', fontSize: '10px' }}
              />
            </div>
          )}
        </div>
      }
    >
      {/* Card Content - Fixed Layout */}
      <div className="p-3 flex flex-col flex-1">
        {/* Skip Details - Fixed Height */}
        <div className="space-y-2 mb-3" style={{ minHeight: '60px' }}>
          <div className="flex items-center gap-2">
            <Ruler className="w-4 h-4 text-blue-400" />
            <Text className="text-slate-300 text-sm">
              {skip.dimensions}
            </Text>
          </div>

          <div className="flex items-center gap-2">
            <Package className="w-4 h-4 text-green-400" />
            <Text className="text-slate-300 text-sm">
              {skip.capacity}
            </Text>
          </div>
        </div>

        {/* Suitable For Tags - Fixed Height */}
        <div className="mb-3" style={{ minHeight: '70px' }}>
          <Text className="text-slate-400 text-xs mb-1 block">Suitable for:</Text>
          <div className="flex flex-wrap gap-1">
            {skip.suitableFor.slice(0, 2).map((use, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-slate-700/50 text-slate-300 text-xs rounded-full"
              >
                {use}
              </span>
            ))}
            {skip.suitableFor.length > 2 && (
              <span className="px-2 py-1 bg-slate-700/50 text-slate-400 text-xs rounded-full">
                +{skip.suitableFor.length - 2} more
              </span>
            )}
          </div>
        </div>

        {/* Price Section - Fixed Height */}
        <div className="mb-3" style={{ minHeight: '60px' }}>
          <div className="flex items-baseline justify-between">
            <Title level={3} className="!text-blue-400 !mb-0">
              £{skip.price}
            </Title>
            <div className="text-right">
              <Text className="text-slate-400 text-xs block">Max weight</Text>
              <Text className="text-white text-sm font-semibold">
                {skip.maxWeight}
              </Text>
            </div>
          </div>
          <Text className="text-slate-400 text-sm">
            {skip.hirePeriod}
          </Text>
        </div>

        {/* Action Button - Always at bottom */}
        <div className="mt-auto">
          <Button
            type={isSelected ? "primary" : "default"}
            size="large"
            onClick={handleSelect}
            block
            className={`
              ${isSelected
                ? 'bg-blue-600 border-blue-600 text-white'
                : 'bg-white border-white text-slate-900 hover:bg-slate-100'
              }
            `}
          >
            {isSelected ? 'Selected' : 'Select This Skip'}
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default SkipCard;
