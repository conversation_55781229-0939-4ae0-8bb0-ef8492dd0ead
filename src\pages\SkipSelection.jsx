import { useState } from 'react';
import { <PERSON>, Col, Typography, Button, Space, Divider } from 'antd';
import { <PERSON>Lef<PERSON>, ArrowR<PERSON>, Filter } from 'lucide-react';
import SkipCard from '../components/SkipCard';
import skipOptions from '../data/skipData';

const { Title, Text } = Typography;

const SkipSelection = () => {
  const [selectedSkip, setSelectedSkip] = useState(null);
  const [filterBy, setFilterBy] = useState('all'); // all, popular, small, large

  // Filter skips based on selected filter
  const filteredSkips = skipOptions.filter(skip => {
    switch (filterBy) {
      case 'popular':
        return skip.popular;
      case 'small':
        return skip.yards <= 6;
      case 'large':
        return skip.yards > 6;
      default:
        return true;
    }
  });

  const handleSkipSelect = (skip) => {
    setSelectedSkip(skip);
  };

  const handleContinue = () => {
    if (selectedSkip) {
      console.log('Selected skip:', selectedSkip);
      // Navigate to next step
    }
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Header Section */}
        <div className="text-center mb-8">
          <Title level={1} className="!text-white !mb-4">
            Choose Your Skip Size
          </Title>
          <Text className="text-slate-300 text-lg">
            Select the skip size that best suits your needs
          </Text>
        </div>

        {/* Filter Buttons */}
        <div className="flex justify-center mb-8">
          <Space size="middle">
            <Button
              type={filterBy === 'all' ? 'primary' : 'default'}
              onClick={() => setFilterBy('all')}
              icon={<Filter className="w-4 h-4" />}
            >
              All Skips
            </Button>
            <Button
              type={filterBy === 'popular' ? 'primary' : 'default'}
              onClick={() => setFilterBy('popular')}
            >
              Popular
            </Button>
            <Button
              type={filterBy === 'small' ? 'primary' : 'default'}
              onClick={() => setFilterBy('small')}
            >
              Small (≤6 yards)
            </Button>
            <Button
              type={filterBy === 'large' ? 'primary' : 'default'}
              onClick={() => setFilterBy('large')}
            >
              Large (>6 yards)
            </Button>
          </Space>
        </div>

        {/* Skip Cards Grid */}
        <Row gutter={[24, 24]} className="mb-8">
          {filteredSkips.map((skip) => (
            <Col key={skip.id} xs={24} sm={12} lg={8} xl={6}>
              <SkipCard
                skip={skip}
                onSelect={handleSkipSelect}
                isSelected={selectedSkip?.id === skip.id}
              />
            </Col>
          ))}
        </Row>

        {/* Selected Skip Summary */}
        {selectedSkip && (
          <>
            <Divider className="border-slate-700" />
            <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-lg p-6 mb-8">
              <Title level={3} className="!text-white !mb-4">
                Selected Skip Summary
              </Title>
              <Row gutter={[24, 16]}>
                <Col xs={24} md={12}>
                  <div className="space-y-2">
                    <Text className="text-slate-300">
                      <strong className="text-white">Skip Size:</strong> {selectedSkip.sizeLabel}
                    </Text>
                    <br />
                    <Text className="text-slate-300">
                      <strong className="text-white">Dimensions:</strong> {selectedSkip.dimensions}
                    </Text>
                    <br />
                    <Text className="text-slate-300">
                      <strong className="text-white">Capacity:</strong> {selectedSkip.capacity}
                    </Text>
                  </div>
                </Col>
                <Col xs={24} md={12}>
                  <div className="space-y-2">
                    <Text className="text-slate-300">
                      <strong className="text-white">Max Weight:</strong> {selectedSkip.maxWeight}
                    </Text>
                    <br />
                    <Text className="text-slate-300">
                      <strong className="text-white">Hire Period:</strong> {selectedSkip.hirePeriod}
                    </Text>
                    <br />
                    <Text className="text-slate-300">
                      <strong className="text-white">Price:</strong> <span className="text-blue-400 text-xl font-bold">£{selectedSkip.price}</span>
                    </Text>
                  </div>
                </Col>
              </Row>
            </div>
          </>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between items-center">
          <Button
            size="large"
            icon={<ArrowLeft className="w-4 h-4" />}
            className="bg-slate-700 border-slate-600 text-white hover:bg-slate-600"
          >
            Back
          </Button>

          <Button
            type="primary"
            size="large"
            disabled={!selectedSkip}
            onClick={handleContinue}
            icon={<ArrowRight className="w-4 h-4" />}
            iconPosition="end"
            className="bg-blue-600 border-blue-600 disabled:bg-slate-700 disabled:border-slate-600"
          >
            Continue to Permit Check
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SkipSelection;
