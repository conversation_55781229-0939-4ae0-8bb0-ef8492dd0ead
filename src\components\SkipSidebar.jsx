import { Card, Typography, Button } from 'antd';
import { ArrowRight } from 'lucide-react';

const { Title, Text } = Typography;

const SkipSidebar = ({ selectedSkip, onContinue }) => {
  return (
    <div className="w-80 bg-slate-800/50 backdrop-blur-sm border-l border-slate-700/50 p-6 min-h-screen">
      {/* Selected Skip Header */}
      <div className="mb-6">
        <Title level={3} className="!text-white !mb-2">
          Selected Skip
        </Title>
      </div>

      {/* Selected Skip Content */}
      {selectedSkip ? (
        <div className="space-y-6">
          {/* Skip Info Card */}
          <Card className="bg-slate-700/50 border-slate-600/50">
            <div className="text-center">
              <Title level={4} className="!text-white !mb-2">
                {selectedSkip.sizeLabel}
              </Title>
              <div className="inline-block px-3 py-1 bg-orange-500 text-white text-sm rounded-full mb-4">
                Popular Choice
              </div>
            </div>
          </Card>

          {/* Skip Details */}
          <div className="space-y-4">
            <Title level={4} className="!text-white !mb-3">
              Skip Details
            </Title>
            
            <div className="space-y-3">
              <div>
                <Text className="text-slate-400 text-sm block">Dimensions</Text>
                <Text className="text-white">{selectedSkip.dimensions}</Text>
              </div>
              
              <div>
                <Text className="text-slate-400 text-sm block">Capacity</Text>
                <Text className="text-white">{selectedSkip.capacity}</Text>
              </div>
              
              <div>
                <Text className="text-slate-400 text-sm block">Max Weight</Text>
                <Text className="text-white">{selectedSkip.maxWeight}</Text>
              </div>
              
              <div>
                <Text className="text-slate-400 text-sm block">Hire Period</Text>
                <Text className="text-white">{selectedSkip.hirePeriod}</Text>
              </div>
            </div>
          </div>

          {/* Suitable For */}
          <div>
            <Title level={4} className="!text-white !mb-3">
              Suitable For
            </Title>
            <div className="space-y-2">
              {selectedSkip.suitableFor.map((use, index) => (
                <div key={index} className="text-white text-sm">
                  • {use}
                </div>
              ))}
            </div>
          </div>

          {/* Price */}
          <div className="bg-slate-700/30 rounded-lg p-4">
            <div className="text-center">
              <Title level={2} className="!text-blue-400 !mb-1">
                £{selectedSkip.price}
              </Title>
              <Text className="text-slate-300">
                {selectedSkip.hirePeriod}
              </Text>
            </div>
          </div>

          {/* Continue Button */}
          <Button
            type="primary"
            size="large"
            block
            onClick={onContinue}
            icon={<ArrowRight className="w-4 h-4" />}
            iconPosition="end"
            className="bg-blue-600 border-blue-600 mt-6"
          >
            Continue
          </Button>
        </div>
      ) : (
        <div className="text-center py-12">
          <Text className="text-slate-400">
            Select a skip to see details
          </Text>
        </div>
      )}
    </div>
  );
};

export default SkipSidebar;
