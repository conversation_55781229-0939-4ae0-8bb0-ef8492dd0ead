import React from 'react';
import { <PERSON>, Typo<PERSON>, <PERSON><PERSON>, Divider, Checkbox } from "antd";
import { ArrowRight, Plus, Minus, Truck, Ruler, Package } from "lucide-react";
import { useSelector, useDispatch } from 'react-redux';
import {
  selectSelectedSkip,
  selectQuantity,
  selectExtendedHire,
  selectExtendedHirePrice,
  selectExtendedHireDays,
  selectCartTotal,
  incrementQuantity,
  decrementQuantity,
  toggleExtendedHire
} from '../store/cartSlice';

const { Title, Text } = Typography;

const SkipSidebar = ({ onContinue }) => {
  const dispatch = useDispatch();
  const selectedSkip = useSelector(selectSelectedSkip);
  const quantity = useSelector(selectQuantity);
  const extendedHire = useSelector(selectExtendedHire);
  const extendedHirePrice = useSelector(selectExtendedHirePrice);
  const extendedHireDays = useSelector(selectExtendedHireDays);
  const cartTotal = useSelector(selectCartTotal);

  const handleQuantityChange = (type) => {
    if (type === 'increment') {
      dispatch(incrementQuantity());
    } else {
      dispatch(decrementQuantity());
    }
  };

  const handleExtendedHireToggle = () => {
    dispatch(toggleExtendedHire());
  };

  return (
    <div className="w-80 bg-slate-800/50 backdrop-blur-sm border-l border-slate-700/50 p-6 min-h-screen">
      {/* Selected Skip Header */}
      <div className="mb-6">
        <Title level={3} className="!text-white !mb-2">
          Selected Skip
        </Title>
      </div>

      {selectedSkip ? (
        <div className="space-y-6">
          {/* Skip Details */}
          <Card className="bg-slate-700/50 border-slate-600">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 bg-slate-600 rounded-lg flex items-center justify-center">
                <Truck className="w-5 h-5 text-blue-400" />
              </div>
              <div>
                <Title level={5} className="!text-white !mb-0">
                  {selectedSkip.sizeLabel}
                </Title>
                <Text className="text-slate-400 text-sm">
                  {selectedSkip.hirePeriod}
                </Text>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Ruler className="w-4 h-4 text-blue-400" />
                <Text className="text-slate-300 text-sm">
                  {selectedSkip.dimensions}
                </Text>
              </div>
              <div className="flex items-center gap-2">
                <Package className="w-4 h-4 text-green-400" />
                <Text className="text-slate-300 text-sm">
                  {selectedSkip.capacity}
                </Text>
              </div>
            </div>
          </Card>

          {/* Customize Your Order */}
          <Card className="bg-slate-700/50 border-slate-600">
            <Title level={4} className="!text-white !mb-4">
              Customize Your Order
            </Title>

            {/* Quantity */}
            <div className="mb-4">
              <Text className="text-slate-300 text-sm block mb-2">Quantity</Text>
              <div className="flex items-center gap-3">
                <Button
                  size="small"
                  onClick={() => handleQuantityChange('decrement')}
                  disabled={quantity <= 1}
                  className="w-8 h-8 flex items-center justify-center bg-slate-600 border-slate-500 text-white hover:bg-slate-500"
                >
                  <Minus className="w-4 h-4" />
                </Button>
                <span className="text-white font-semibold text-lg w-8 text-center">
                  {quantity}
                </span>
                <Button
                  size="small"
                  onClick={() => handleQuantityChange('increment')}
                  disabled={quantity >= 10}
                  className="w-8 h-8 flex items-center justify-center bg-slate-600 border-slate-500 text-white hover:bg-slate-500"
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Extended Hire */}
            <div className="mb-4">
              <Checkbox
                checked={extendedHire}
                onChange={handleExtendedHireToggle}
                className="text-slate-300"
              >
                <Text className="text-slate-300">
                  Extended hire (+{extendedHireDays} days) - £{extendedHirePrice}
                </Text>
              </Checkbox>
            </div>
          </Card>

          {/* Price Breakdown */}
          <Card className="bg-slate-700/50 border-slate-600">
            <Title level={4} className="!text-white !mb-4">
              Price Breakdown
            </Title>

            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <Text className="text-slate-300">
                  {selectedSkip.sizeLabel} x {quantity}
                </Text>
                <Text className="text-white font-semibold">
                  £{selectedSkip.price * quantity}
                </Text>
              </div>

              {extendedHire && (
                <div className="flex justify-between items-center">
                  <Text className="text-slate-300">
                    Extended hire (+{extendedHireDays} days)
                  </Text>
                  <Text className="text-white font-semibold">
                    £{extendedHirePrice}
                  </Text>
                </div>
              )}

              <Divider className="!bg-slate-600 !my-3" />

              <div className="flex justify-between items-center">
                <Title level={4} className="!text-white !mb-0">
                  Total
                </Title>
                <Title level={4} className="!text-blue-400 !mb-0">
                  £{cartTotal}
                </Title>
              </div>

              <Text className="text-slate-400 text-xs">
                *Prices include VAT and delivery
              </Text>
            </div>
          </Card>

          {/* Continue Button */}
          <Button
            type="primary"
            size="large"
            block
            onClick={onContinue}
            className="bg-blue-600 hover:bg-blue-700 border-blue-600 h-12"
          >
            Continue to Next Step
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      ) : (
        <div className="text-center py-12">
          <Text className="text-slate-400">Select a skip to see details</Text>
        </div>
      )}
    </div>
  );
};

export default SkipSidebar;
