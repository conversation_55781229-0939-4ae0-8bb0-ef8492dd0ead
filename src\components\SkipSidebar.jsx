import React from 'react';
import { Typo<PERSON>, But<PERSON>, Checkbox } from "antd";
import { ArrowRight, Plus, Minus } from "lucide-react";
import { useSelector, useDispatch } from 'react-redux';
import {
  selectSelectedSkip,
  selectQuantity,
  selectExtendedHire,
  selectExtendedHirePrice,
  selectExtendedHireDays,
  selectCartTotal,
  incrementQuantity,
  decrementQuantity,
  toggleExtendedHire
} from '../store/cartSlice';

const { Title, Text } = Typography;

const SkipSidebar = ({ onContinue }) => {
  const dispatch = useDispatch();
  const selectedSkip = useSelector(selectSelectedSkip);
  const quantity = useSelector(selectQuantity);
  const extendedHire = useSelector(selectExtendedHire);
  const extendedHirePrice = useSelector(selectExtendedHirePrice);
  const extendedHireDays = useSelector(selectExtendedHireDays);
  const cartTotal = useSelector(selectCartTotal);

  const handleQuantityChange = (type) => {
    if (type === 'increment') {
      dispatch(incrementQuantity());
    } else {
      dispatch(decrementQuantity());
    }
  };

  const handleExtendedHireToggle = () => {
    dispatch(toggleExtendedHire());
  };

  return (
    <div className="w-80 bg-slate-800/50 backdrop-blur-sm border-l border-slate-700/50 min-h-screen">
      {/* Sticky Container */}
      <div className="sticky top-0 h-screen overflow-y-auto p-6 scrollbar-thin scrollbar-thumb-slate-600 scrollbar-track-slate-800">
        {/* Selected Skip Header */}
        <div className="mb-6">
          <Title level={3} className="!text-white !mb-2">
            Selected Skip
          </Title>
        </div>

      <div className="space-y-6">
        {/* Selected Skip Header Card */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl p-4 text-center">
          <Title level={4} className="!text-white !mb-2">
            {selectedSkip ? selectedSkip.sizeLabel : '8 Yard Skip'}
          </Title>
          {(selectedSkip?.popular || !selectedSkip) && (
            <div className="inline-flex items-center gap-1 bg-orange-500 text-white px-3 py-1 rounded-full text-xs font-medium">
              <span>🔥</span>
              Popular Choice
            </div>
          )}
        </div>



        {/* Customize Your Order */}
        <div className="bg-slate-800/80 border border-slate-700/50 rounded-xl p-5">
          <Title level={4} className="!text-white !mb-4">
            Customize Your Order
          </Title>

          {/* Quantity */}
          <div className="mb-6">
            <Text className="text-slate-400 text-sm block mb-3">Quantity</Text>
            <div className="flex items-center justify-center gap-4 bg-slate-700/50 rounded-lg p-3">
              <Button
                size="small"
                onClick={() => handleQuantityChange('decrement')}
                disabled={quantity <= 1}
                className="w-10 h-10 flex items-center justify-center bg-slate-600 border-slate-500 text-white hover:bg-slate-500 disabled:bg-slate-700 disabled:text-slate-500 rounded-lg"
              >
                <Minus className="w-4 h-4" />
              </Button>
              <div className="bg-slate-800 border border-slate-600 rounded-lg px-4 py-2 min-w-[60px] text-center">
                <span className="text-white font-bold text-xl">
                  {quantity}
                </span>
              </div>
              <Button
                size="small"
                onClick={() => handleQuantityChange('increment')}
                disabled={quantity >= 10}
                className="w-10 h-10 flex items-center justify-center bg-slate-600 border-slate-500 text-white hover:bg-slate-500 disabled:bg-slate-700 disabled:text-slate-500 rounded-lg"
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Extended Hire */}
          <div className="bg-slate-700/30 border border-slate-600/50 rounded-lg p-4">
            <Checkbox
              checked={extendedHire}
              onChange={handleExtendedHireToggle}
              className="text-slate-400 [&_.ant-checkbox]:bg-slate-700 [&_.ant-checkbox]:border-slate-500 [&_.ant-checkbox-checked]:bg-blue-600 [&_.ant-checkbox-checked]:border-blue-600"
            >
              <div className="flex flex-col">
                <Text className="text-slate-200 font-medium">
                  Extended hire (+{extendedHireDays} days)
                </Text>
                <Text className="text-slate-400 text-xs">
                  Additional £{extendedHirePrice} for extended rental period
                </Text>
              </div>
            </Checkbox>
          </div>
        </div>

        {/* Price Breakdown */}
        <div className="bg-slate-800/80 border border-slate-700/50 rounded-xl p-5">
          <Title level={4} className="!text-white !mb-4">
            Price Breakdown
          </Title>

          <div className="space-y-4">
            <div className="flex justify-between items-center py-2 border-b border-slate-700/50">
              <Text className="text-slate-400">
                {selectedSkip ? selectedSkip.sizeLabel : '8 Yard Skip'} x {quantity}
              </Text>
              <Text className="text-white font-semibold">
                £{selectedSkip ? selectedSkip.price * quantity : 325 * quantity}
              </Text>
            </div>

            {extendedHire && (
              <div className="flex justify-between items-center py-2 border-b border-slate-700/50">
                <Text className="text-slate-400">
                  Extended hire (+{extendedHireDays} days)
                </Text>
                <Text className="text-white font-semibold">
                  £{extendedHirePrice}
                </Text>
              </div>
            )}

            <div className="bg-gradient-to-r from-blue-600/20 to-blue-700/20 border border-blue-500/30 rounded-lg p-4 mt-4">
              <div className="flex justify-between items-center">
                <Title level={4} className="!text-white !mb-0">
                  Total
                </Title>
                <Title level={3} className="!text-blue-400 !mb-0">
                  £{selectedSkip ? cartTotal : 325}
                </Title>
              </div>
            </div>

            <Text className="text-slate-400 text-xs text-center">
              *Prices include VAT and delivery
            </Text>
          </div>
        </div>

        {/* Continue Button */}
        <Button
          type="primary"
          size="large"
          block
          onClick={onContinue}
          className="bg-blue-600 hover:bg-blue-700 border-blue-600 h-12"
        >
          Continue to Next Step
          <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
        </div>
      </div>
    </div>
  );
};

export default SkipSidebar;
